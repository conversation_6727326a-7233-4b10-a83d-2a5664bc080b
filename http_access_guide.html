<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Corporate Prompt Master - HTTP Access Guide</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .url-box {
            background-color: #f8f9fa;
            border: 2px solid #007bff;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 18px;
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            font-weight: bold;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        ul {
            line-height: 1.6;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Corporate Prompt Master - HTTP Development Server</h1>
        
        <div class="success">
            <strong>✅ Server is running in HTTP mode!</strong><br>
            The development server is correctly configured for HTTP-only access.
        </div>

        <h2>🌐 Correct Access URLs</h2>
        <div class="url-box">
            <strong>Main Application:</strong><br>
            http://localhost:8000/
        </div>
        
        <div style="text-align: center;">
            <a href="http://localhost:8000/" class="btn">🎮 Launch Game</a>
            <a href="http://localhost:8000/admin/" class="btn">⚙️ Admin Panel</a>
            <a href="http://localhost:8000/corporate/" class="btn">🏢 Corporate Dashboard</a>
        </div>

        <div class="warning">
            <strong>⚠️ Important:</strong> Always use <code>http://</code> (not <code>https://</code>) when accessing the development server.
        </div>

        <h2>🔧 Troubleshooting HTTPS Errors</h2>
        
        <div class="error">
            <strong>If you see "Bad request version" errors:</strong><br>
            This means something is trying to access the server via HTTPS. The Django development server only supports HTTP.
        </div>

        <h3>Solutions:</h3>
        <ul>
            <li><strong>Clear browser cache:</strong> Press Ctrl+Shift+Delete and clear browsing data</li>
            <li><strong>Use incognito/private mode:</strong> This bypasses cached redirects</li>
            <li><strong>Check bookmarks:</strong> Make sure saved bookmarks use <code>http://</code> not <code>https://</code></li>
            <li><strong>Disable HTTPS redirects:</strong> Some browser extensions force HTTPS</li>
            <li><strong>Type URL manually:</strong> Always type <code>http://localhost:8000/</code> in the address bar</li>
        </ul>

        <h2>🛠️ Development Commands</h2>
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;">
            # Start the development server<br>
            python manage.py runserver 0.0.0.0:8000<br><br>
            
            # Or use the development script<br>
            python run_dev.py<br><br>
            
            # Windows batch file<br>
            run_dev.bat
        </div>

        <h2>📋 Server Status Check</h2>
        <p>If you can see this page, your HTTP development server is working correctly!</p>
        
        <div class="success">
            <strong>✅ Configuration Status:</strong>
            <ul>
                <li>DEBUG = True ✅</li>
                <li>SECURE_SSL_REDIRECT = False ✅</li>
                <li>HTTP-only cookies ✅</li>
                <li>CORS enabled for development ✅</li>
                <li>Security middleware disabled ✅</li>
            </ul>
        </div>
    </div>
</body>
</html>
