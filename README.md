# Corporate Prompt Master Game - Django Edition

This is a Django version of the Corporate Prompt Master game, a role-playing game where you progress through a corporate hierarchy by completing prompt engineering challenges.

## Features

- Role-playing game with multiple corporate roles
- Progress through the corporate hierarchy by completing challenges
- Interactive chat interface with AI managers
- Visual organization chart and role progression
- Dark/light mode support
- Responsive design for mobile, tablet, and desktop

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd prompt_game
```

2. Create a virtual environment and activate it:
```bash
python -m venv venv
# On Windows
venv\Scripts\activate
# On macOS/Linux
source venv/bin/activate
```

3. Install the required packages:
```bash
pip install -r requirements.txt
```

4. Apply migrations:
```bash
python manage.py migrate
```

5. Run the development server:

**Option A: Using Django's built-in command**
```bash
python manage.py runserver 0.0.0.0:8000
```

**Option B: Using the provided development script**
```bash
# On Windows
run_dev.bat

# On any platform
python run_dev.py
```

6. Open your browser and navigate to http://localhost:8000/ or http://127.0.0.1:8000/

## Development Configuration

This project is configured to run using **HTTP** in development mode with the following settings:

- **Debug Mode**: Enabled (`DEBUG = True`)
- **Protocol**: HTTP (no SSL/HTTPS required)
- **CORS**: Configured for local development
- **Security**: Development-friendly settings (security middleware disabled)
- **Static Files**: Auto-refresh enabled for development

### Development URLs
- Main Application: http://localhost:8000/
- Admin Interface: http://localhost:8000/admin/
- Game Interface: http://localhost:8000/game/
- Corporate Dashboard: http://localhost:8000/corporate/

### Creating a Superuser
To access the admin interface, create a superuser account:
```bash
python manage.py createsuperuser
```

## 🔧 Troubleshooting HTTPS Errors

If you see errors like:
```
[09/Jul/2025 14:09:48] code 400, message Bad request version ('\x84')
[09/Jul/2025 14:09:48] You're accessing the development server over HTTPS, but it only supports HTTP.
```

**This means something is trying to access the server via HTTPS.** The Django development server only supports HTTP.

### Solutions:

1. **Always use HTTP URLs:**
   - ✅ Correct: `http://localhost:8000/`
   - ❌ Wrong: `https://localhost:8000/`

2. **Clear browser cache:**
   - Press `Ctrl+Shift+Delete` and clear browsing data
   - Or use incognito/private browsing mode

3. **Check bookmarks and history:**
   - Make sure saved bookmarks use `http://` not `https://`
   - Clear browser history if needed

4. **Disable browser HTTPS enforcement:**
   - Some browsers or extensions automatically upgrade HTTP to HTTPS
   - Disable "HTTPS Everywhere" or similar extensions for localhost

5. **Type URLs manually:**
   - Always type `http://localhost:8000/` directly in the address bar
   - Don't rely on autocomplete which might use cached HTTPS URLs

## Game Structure

The game is structured around a corporate hierarchy with multiple roles:

1. **Applicant**: The starting role
2. **Junior Assistant**: Entry-level position
3. **Sales Associate**: First promotion
4. **Marketing Associate**: Second promotion
5. And many more roles up to CEO

Each role has specific tasks that must be completed to advance to the next role. Tasks are evaluated based on how well they meet the requirements.

## Technical Details

- Built with Django 5.x
- Uses SQLite for database storage
- JavaScript for frontend interactivity
- Markdown for formatting messages
- OpenAI API for response evaluation (optional)

## Configuration

You can configure the game by setting environment variables in a `.env` file:

```
OPENAI_API_KEY=your_api_key_here
USE_LLM_EVALUATION=True
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
