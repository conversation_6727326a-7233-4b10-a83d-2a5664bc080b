#!/usr/bin/env python
"""
Development server runner for the Corporate Prompt Master Django project.
This script runs the Django development server with appropriate settings for HTTP development.
"""

import os
import sys
import subprocess

def main():
    """Run the Django development server."""
    
    # Set the Django settings module
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
    
    # Check if manage.py exists
    if not os.path.exists('manage.py'):
        print("Error: manage.py not found. Make sure you're in the project root directory.")
        sys.exit(1)
    
    print("\n" + "=" * 70)
    print("Starting Corporate Prompt Master - Django Development Server")
    print("=" * 70)
    print("\n🚀 Running on HTTP for development...")
    print("📍 IMPORTANT: Use HTTP (not HTTPS) URLs:")
    print("   Main App: http://localhost:8000/")
    print("   Admin:    http://localhost:8000/admin/")
    print("   Game:     http://localhost:8000/game/")
    print("\n⚠️  If you see 'Bad request version' errors, you're using HTTPS.")
    print("   Always use http:// (not https://) in your browser!")
    print("=" * 70 + "\n")
    
    try:
        # Run Django development server
        subprocess.run([
            sys.executable, 'manage.py', 'runserver', 
            '0.0.0.0:8000',  # Bind to all interfaces on port 8000
            '--settings=prompt_game.settings'
        ], check=True)
    except KeyboardInterrupt:
        print("\n\nShutting down development server...")
    except subprocess.CalledProcessError as e:
        print(f"\nError running development server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
